package admin

import (
	"context"
	"fmt"
	"member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCertificationListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取待审核认证列表
func NewGetPendingCertificationListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCertificationListLogic {
	return &GetPendingCertificationListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPendingCertificationListLogic) GetPendingCertificationList(req *types.GetPendingListReq) (resp *types.GetPendingListResp, err error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}

	// 调用RPC服务获取待审核认证列表
	rpcResp, err := l.svcCtx.MemberRpc.GetPendingCertificationList(l.ctx, &member.GetPendingCertificationListReq{
		Page:     int32(req.Page),
		PageSize: int32(req.PageSize),
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC服务的GetPendingCertificationList方法失败: %v", err)
	}

	if rpcResp.Code != 200 {
		return &types.GetPendingListResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	// 构建响应数据
	var pendingList []types.PendingCertification
	for _, cert := range rpcResp.List {
		// 获取用户基础信息
		memberInfo, memberErr := l.svcCtx.MemberRpc.GetMemberInfo(l.ctx, &member.GetMemberInfoReq{
			MemberId: cert.MemberId,
		})

		var username, mobile string
		if memberErr == nil && memberInfo.Code == 200 && memberInfo.Data != nil {
			username = memberInfo.Data.Username
			mobile = memberInfo.Data.Mobile
		}

		pendingList = append(pendingList, types.PendingCertification{
			Id:           cert.Id,
			MemberId:     cert.MemberId,
			Username:     username,
			Mobile:       mobile,
			Realname:     cert.Realname,
			IdentityCard: cert.IdentityCard,
			SubmitTime:   cert.CreatedAt,
			Status:       int(cert.Status),
		})
	}

	return &types.GetPendingListResp{
		Code:    200,
		Message: "获取待审核认证列表成功",
		Data: types.PendingListData{
			List: pendingList,
			PageInfo: types.PageInfo{
				Page:     int(rpcResp.PageInfo.Page),
				PageSize: int(rpcResp.PageInfo.PageSize),
				Total:    int(rpcResp.PageInfo.Total),
			},
		},
	}, nil
}
