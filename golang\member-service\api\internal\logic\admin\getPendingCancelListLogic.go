package admin

import (
	"context"
	"fmt"
	"member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCancelListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取待审核注销列表
func NewGetPendingCancelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCancelListLogic {
	return &GetPendingCancelListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPendingCancelListLogic) GetPendingCancelList(req *types.GetPendingCancelListReq) (resp *types.GetPendingCancelListResp, err error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}

	// 调用RPC服务获取待审核注销申请列表
	rpcResp, err := l.svcCtx.MemberRpc.GetPendingCancelList(l.ctx, &member.GetPendingCancelListReq{
		Page:     int32(req.Page),
		PageSize: int32(req.PageSize),
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC服务的GetPendingCancelList方法失败: %v", err)
	}

	if rpcResp.Code != 200 {
		return &types.GetPendingCancelListResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	// 构建响应数据
	var pendingList []types.PendingCancelItem
	for _, cancel := range rpcResp.List {
		// 获取用户基础信息
		memberInfo, memberErr := l.svcCtx.MemberRpc.GetMemberInfo(l.ctx, &member.GetMemberInfoReq{
			MemberId: cancel.MemberId,
		})

		var username, nickname, mobile string
		if memberErr == nil && memberInfo.Code == 200 && memberInfo.Data != nil {
			username = memberInfo.Data.Username
			nickname = memberInfo.Data.Nickname
			mobile = memberInfo.Data.Mobile
		}

		pendingList = append(pendingList, types.PendingCancelItem{
			Id:           cancel.Id,
			MemberId:     cancel.MemberId,
			Username:     username,
			Nickname:     nickname,
			Mobile:       mobile,
			Content:      cancel.Content,
			SubmitTime:   cancel.CreatedAt,
			AuditStatus:  int(cancel.AuditStatus),
			RefusalCause: cancel.RefusalCause,
		})
	}

	return &types.GetPendingCancelListResp{
		Code:    200,
		Message: "获取待审核注销申请列表成功",
		Data: types.PendingCancelListData{
			List: pendingList,
			PageInfo: types.PageInfo{
				Page:     int(rpcResp.PageInfo.Page),
				PageSize: int(rpcResp.PageInfo.PageSize),
				Total:    int(rpcResp.PageInfo.Total),
			},
		},
	}, nil
}
